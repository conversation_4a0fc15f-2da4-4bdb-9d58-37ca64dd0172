<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Enquiry Search View -->
    <record id="view_enquiry_search" model="ir.ui.view">
        <field name="name">enquiry.enquiry.search</field>
        <field name="model">enquiry.enquiry</field>
        <field name="arch" type="xml">
            <search string="Enquiries">
                <field name="name" string="Enquiry ID"/>
                <field name="customer_name" string="Customer Name"/>
                <field name="company_name"/>
                <field name="project_name"/>
                <field name="company_poc_id"/>
                <field name="bd_poc_id"/>
                <separator/>
                <filter string="My Enquiries" name="my_enquiries" domain="[('company_poc_id.user_id', '=', uid)]"/>
                <filter string="Requires NDA" name="nda_required" domain="[('nda_required', '=', True)]"/>
                <filter string="Pending Registration" name="pending_registration" domain="[('completed_registration', '=', False)]"/>
                <filter string="Awaiting Approval" name="filter_awaiting_approval" domain="[('quotation_approved', '=', False), ('stage_id.name', '=', 'Pre-Sales')]"/>
                <separator/>
                <filter string="High Priority" name="high_priority" domain="[('priority', 'in', ['2', '3'])]"/>
                <separator/>
                <group expand="0" string="Group By">
                    <filter name="group_stage" string="Stage" context="{'group_by': 'stage_id'}"/>
                    <filter name="group_priority" string="Priority" context="{'group_by': 'priority'}"/>
                    <filter name="group_poc" string="Company PoC" context="{'group_by': 'company_poc_id'}"/>
                    <filter name="group_enquiry_type" string="Enquiry Type" context="{'group_by': 'enquiry_type'}"/>
                    <filter name="group_created_date" string="Created Date" context="{'group_by': 'create_date:month'}"/>
                </group>
            </search>
        </field>
    </record>
 
    <!-- Enquiry Tree View -->
    <record id="view_enquiry_tree" model="ir.ui.view">
        <field name="name">enquiry.enquiry.tree</field>
        <field name="model">enquiry.enquiry</field>
        <field name="arch" type="xml">
            <tree string="Enquiries" sample="1">
                <field name="name"/>
                <field name="create_date" widget="date"/>
                <field name="customer_name"/>
                <field name="company_name"/>
                <field name="project_name"/>
                <field name="stage_id"/>
                <field name="stage_duration_human"/>
                <field name="enquiry_type"/>
                <field name="completed_registration" widget="boolean_toggle"/>
                <field name="bd_poc_id"/>
                <field name="followup_date"/>
            </tree>
        </field>
    </record>
 
    <!-- Enquiry Kanban View -->
    <record id="view_enquiry_kanban" model="ir.ui.view">
        <field name="name">enquiry.enquiry.kanban</field>
        <field name="model">enquiry.enquiry</field>
        <field name="arch" type="xml">
            <kanban default_group_by="stage_id" class="o_kanban_small_column" sample="1" create="1" edit="0" quick_create="0" group_delete="0" group_edit="0" records_draggable="0">
                <field name="name"/>
                <field name="customer_name"/>
                <field name="company_name"/>
                <field name="project_name"/>
                <field name="priority"/>
                <field name="stage_id"/>
                <field name="enquiry_type"/>
                <field name="completed_registration"/>
                <field name="company_poc_id"/>
                <field name="bd_poc_id"/>
                <field name="followup_date"/>
                <field name="nda_required"/>
                <field name="total_cost"/>
                <field name="quotation_approved"/>
                <field name="stage_duration_human"/>
                <field name="fabrication_capabilities_display"/>
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="oe_kanban_card oe_kanban_global_click" style="padding: 0; border-radius: 8px; overflow: hidden;">
                            <div class="o_dropdown_kanban dropdown position-absolute" style="right: 8px; top: 8px; z-index: 5;">
                                <a class="dropdown-toggle o-no-caret btn" role="button" data-bs-toggle="dropdown" data-display="static" aria-expanded="false">
                                    <i class="fa fa-ellipsis-v"/>
                                </a>
                                <div class="dropdown-menu" role="menu">
                                    <t t-if="widget.editable">
                                        <a role="menuitem" type="edit" class="dropdown-item">Edit</a>
                                    </t>
                                </div>
                            </div>
                            
                            <!-- Header with ID and time badge -->
                            <div class="bg-light d-flex justify-content-between align-items-center p-2" style="border-bottom: 1px solid #e0e0e0;">
                                <strong class="o_kanban_record_title"><field name="name"/></strong>
                                <div class="badge text-bg-info">
                                    <i class="fa fa-clock-o" title="Time in stage"/> <field name="stage_duration_human"/>
                                </div>
                            </div>
                            
                            <!-- Main content with better spacing -->
                            <div class="p-3">
                                <!-- Customer and company info -->
                                <div class="mb-2">
                                    <field name="customer_name"/> - <field name="company_name"/>
                                </div>
                                
                                <!-- Project name -->
                                <div class="mb-2">
                                    <strong>Project: </strong><field name="project_name"/>
                                </div>
                                
                                <!-- Enquiry type badge -->
                                <div class="mb-2">
                                    <span t-if="record.enquiry_type.raw_value === 'design_engineering'" class="badge text-bg-info">
                                        Design &amp; Engineering (D&amp;E)
                                    </span>
                                    <span t-elif="record.enquiry_type.raw_value === 'fabrication_prototyping'" class="badge text-bg-info">
                                        Fabrication/Prototyping (Fab/Proto)
                                    </span>
                                    <span t-else="" class="badge text-bg-info">
                                        <field name="enquiry_type"/>
                                    </span>
                                </div>
                                
                                <!-- Registration status -->
                                <div class="mb-2">
                                    <t t-if="record.completed_registration.raw_value">
                                        <span class="badge text-bg-success">Registration Complete</span>
                                    </t>
                                    <t t-else="">
                                        <span class="badge text-bg-warning">Registration Pending</span>
                                    </t>
                                    
                                    <t t-if="record.nda_required.raw_value">
                                        <span class="badge text-bg-danger ms-1">NDA Required</span>
                                    </t>
                                </div>
                                
                                <!-- BD PoC info -->
                                <div t-if="record.bd_poc_id.raw_value" class="mb-2">
                                    <strong>BD PoC: </strong><field name="bd_poc_id"/>
                                </div>
                                
                                <!-- Follow-up date -->
                                <div t-if="record.followup_date.raw_value" class="mb-2">
                                    <strong>Follow-up: </strong><field name="followup_date"/>
                                </div>
                                
                                <!-- Cost info with approved badge -->
                                <div t-if="record.total_cost.raw_value" class="mb-2">
                                    <strong>Total Cost: </strong><field name="total_cost" widget="monetary"/>
                                    <t t-if="record.quotation_approved.raw_value">
                                        <span class="badge text-bg-success ms-1">Approved</span>
                                    </t>
                                </div>
                                
                                <!-- Fabrication capabilities with better styling -->
                                <div t-if="record.enquiry_type.raw_value === 'fabrication_prototyping' &amp;&amp; record.fabrication_capabilities_display.raw_value" class="mb-2">
                                    <strong>Capabilities: </strong>
                                    <span class="badge text-bg-dark"><t t-esc="record.fabrication_capabilities_display.value"/></span>
                                </div>
                            </div>
                            
                            <!-- Footer with priority stars and assignee -->
                            <div class="d-flex justify-content-between align-items-center p-2 bg-light" style="border-top: 1px solid #e0e0e0;">
                                <div>
                                    <field name="priority" widget="priority"/>
                                </div>
                                <div>
                                    <field name="company_poc_id" widget="many2one_avatar"/>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>
 
    <!-- Enquiry Form View -->
   <record id="view_enquiry_form" model="ir.ui.view">
       <field name="name">enquiry.enquiry.form</field>
        <field name="model">enquiry.enquiry</field>
        <field name="arch" type="xml">
            <form string="Enquiry" __validate__="1">
                <header>
                    <field name="proforma_invoice_count" invisible="1"/>
                    <button name="action_generate_update_link" string="Generate Update Link" type="object" 
                            class="oe_highlight" invisible="1"/>
                    <button name="action_send_update_link" string="Send Update Link" type="object"
                            class="oe_highlight" invisible="1"/>
                    <!-- <button name="action_get_quote" string="View/Generate Invoice" type="object" 
                            class="oe_highlight" invisible="not quotation_approved"/> -->
                    <button name="action_send_for_approval" string="Send for Approval" type="object" 
                            class="oe_highlight" invisible="1"/>
                    <!-- Removed Approve Quotation button from here -->
                    <button name="sync_presales_to_project" string="SYNC TO PROJECT" type="object" class="btn btn-success"/>
                    <button name="action_close_enquiry" string="Close Enquiry" type="object"/>
                    <field name="stage_id" widget="statusbar" options="{'clickable': '0'}"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <!-- Future buttons for related actions -->
                    </div>
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="create_date" readonly="1"/>
                            <field name="project_name"/>
                            <field name="company_id" invisible="1"/>
                            <field name="can_move_to_presales" invisible="1"/>
                        </group>
                        <group>
                            <field name="completed_registration"/>
                            <field name="quotation_approved" readonly="1"/>
                            <field name="quotation_approved_by_id" readonly="1"/>
                            <field name="quotation_approval_date" readonly="1"/>
                            <field name="stage_time_start" readonly="1"/>
                            <field name="stage_duration_human" readonly="1"/>
                        </group>
                    </group>
 
                    <!-- Project Details Group with Type of Enquiry, Priority, and Verticals -->
                    <group string="Project Details">
                        <field name="enquiry_type"/>
                        <field name="priority" widget="priority"/>
                        <field name="verticals"/>
                        <field name="verticals_other" invisible="verticals != 'others'" required="verticals == 'others'"/>
                    </group>
 
                    <!-- Contact information in single column below Project Details -->
                    <group>
                        <field name="bd_poc_id" string="BD Point of Contact"/>
                        <field name="company_poc_id" readonly="1"/>
                        <field name="followup_date"/>
                    </group>
 
                    <div class="w-100 mt-3 mb-3">
                        <label for="specific_requirements" string="Meeting Summary" class="fw-bold fs-4"/>
                        <field name="specific_requirements" placeholder="Please provide a summary of the meeting including key discussion points, decisions made, and any action items..." 
                               class="mt-2" style="width: 100%; min-height: 20px;"/>
                    </div>
 
                    <notebook>
                        <!-- CRM Tab -->
                        <page string="CRM" name="basic_info">
                            <!-- Add buttons at the top of CRM tab -->
                            <div class="d-flex justify-content-end mb-3">
                                <button name="action_generate_update_link" string="Generate Update Link" type="object" 
                                        class="btn btn-secondary me-2"/>
                                <button name="action_send_update_link" string="Send Update Link" type="object"
                                        class="btn btn-secondary" invisible="1"/>
                            </div>

                            <group>
                                <group string="Client Information">
                                    <field name="customer_name"/>
                                    <field name="email"/>
                                    <field name="phone"/>
                                    <field name="company_name"/>
                                    <field name="gst_number"/>
                                    <field name="pan_number"/>
                                    <field name="gst_registered" readonly="1"/>
                                    <field name="designation"/>
                                    <field name="visitor_type"/>
                                    <field name="visitor_type_other" invisible="visitor_type != 'other'" required="visitor_type == 'other'"/>
                                    <field name="referred_by"/>
                                    <field name="referred_by_other" invisible="referred_by != 'other'" required="referred_by == 'other'"/>
                                    <field name="information_update_link" string="CRM Link" readonly="1"/>
                                </group>
                            </group>
                            <group>
                                <group string="Billing Address">
                                    <field name="billing_street"/>
                                    <field name="billing_city"/>
                                    <field name="billing_zip"/>
                                    <field name="billing_country_id"/>
                                </group>
                                <group string="Shipping Address">
                                    <field name="shipping_same_as_billing"/>
                                    <field name="shipping_street"/>
                                    <field name="shipping_city"/>
                                    <field name="shipping_zip"/>
                                    <field name="shipping_country_id"/>
                                </group>
                            </group>
 
                            <!-- Navigation button at bottom of Information tab -->
                            <div class="d-flex justify-content-end mt-4">
                                <button name="action_move_to_awaiting_requirements" string="Next: Submit to Requirements" type="object" 
                                        class="btn btn-primary px-4"/>
                            </div>
                        </page>
 
                        <!-- Requirements tab -->
                        <page string="Requirements" name="requirements" invisible="stage_id.name in ['Onboarding']">
                            <group>
                                <group string="Product Requirements Document (PRD)" invisible="enquiry_type == 'events_workshops'">
                                    <field name="attachment_ids" widget="many2many_binary" nolabel="1"/>
                                </group>
                                <group string="Project Details">
                                    <field name="company_poc_id"/>
                                    <field name="requested_deadline_date" invisible="enquiry_type != 'design_engineering'"/>
                                </group>
                            </group>
                            <notebook>
                                <page string="Design &amp; Engineering" name="design_requirements" invisible="enquiry_type != 'design_engineering'">
                                    <div class="w-100 mt-3 mb-3">
                                        <label for="design_brief" class="fw-bold"/>
                                        <field name="design_brief" placeholder="Enter detailed design brief and requirements" 
                                            nolabel="1" class="mt-2" style="width: 100%; min-height: 200px;"/>
                                    </div>
                                </page>
                                <page string="Fabrication/Prototyping" name="fabrication_requirements" invisible="enquiry_type != 'fabrication_prototyping'">
                                    <group>
                                        <field name="fabrication_capabilities" widget="many2many_tags" string="Required Capabilities"/>
                                    </group>
 
                                    <!-- Dynamic capability attachments based on selection -->
                                    <div class="o_horizontal_separator mt16 mb16">Capability Attachments</div>
 
                                    <!-- Metal Shop Attachments -->
                                    <div name="metal_shop_div">
                                        <div class="o_horizontal_separator mt8 mb8">Metal Shop Attachments</div>
                                        <field name="metal_shop_attachment_ids" widget="many2many_binary" string="Metal Shop Files"/>
                                    </div>

                                    <!-- 3D Printing Attachments -->
                                    <div name="printing_3d_div">
                                        <div class="o_horizontal_separator mt8 mb8">3D Printing Attachments</div>
                                        <field name="printing_3d_attachment_ids" widget="many2many_binary" string="3D Printing Files"/>
                                    </div>
 
                                    <!-- Laser Cutting Attachments -->
                                    <div name="laser_cutting_div">
                                        <div class="o_horizontal_separator mt8 mb8">Laser Cutting Attachments</div>
                                        <field name="laser_cutting_attachment_ids" widget="many2many_binary" string="Laser Cutting Files"/>
                                    </div>
 
                                    <!-- Wood Shop Attachments -->
                                    <div name="wood_shop_div">
                                        <div class="o_horizontal_separator mt8 mb8">Wood Shop Attachments</div>
                                        <field name="wood_shop_attachment_ids" widget="many2many_binary" string="Wood Shop Files"/>
                                    </div>
 
                                    <!-- Ceramic Studio Attachments -->
                                    <div name="ceramic_studio_div">
                                        <div class="o_horizontal_separator mt8 mb8">Ceramic Studio Attachments</div>
                                        <field name="ceramic_studio_attachment_ids" widget="many2many_binary" string="Ceramic Studio Files"/>
                                    </div>
 
                                    <!-- ARP Attachments -->
                                    <div name="arp_div">
                                        <div class="o_horizontal_separator mt8 mb8">Advanced Rapid Prototyping Attachments</div>
                                        <field name="arp_attachment_ids" widget="many2many_binary" string="ARP Files"/>
                                    </div>
 
                                    <!-- Electronics Attachments -->
                                    <div name="electronics_div">
                                        <div class="o_horizontal_separator mt8 mb8">Electronics Attachments</div>
                                        <field name="electronics_attachment_ids" widget="many2many_binary" string="Electronics Files"/>
                                    </div>
 
                                    <!-- Textile Attachments -->
                                    <div name="textile_div">
                                        <div class="o_horizontal_separator mt8 mb8">Textile Attachments</div>
                                        <field name="textile_attachment_ids" widget="many2many_binary" string="Textile Files"/>
                                    </div>
                                </page>
                                <!-- EVENTS/WORKSHOPS PAGE -->
                                <page string="Event / Workshop Details" name="event_requirements" invisible="enquiry_type != 'events_workshops'">
                                    <group string="Event Details">
                                        <field name="event_name"/>
                                        <field name="event_type"/>
                                        <field name="event_theme"/>
                                        <field name="event_description"/>
                                        <field name="estimated_attendees"/>
                                        <field name="event_venue"/>
                                        <field name="event_time_slot"/>
                                        <field name="event_date_begin"/>
                                        <field name="event_date_end"/>
                                    </group>

                                    <!-- Additional Requirements (match events presales layout exactly) -->
                                    <separator string="Additional Requirements" colspan="2" invisible="enquiry_type != 'events_workshops'"/>
                                    <notebook invisible="enquiry_type != 'events_workshops'">
                                        <page string="Requirements">
                                            <field name="requirement_line_ids" nolabel="1">
                                                <tree editable="bottom">
                                                    <field name="category" readonly="1"/>
                                                    <field name="requirement_id" domain="[('active', '=', True)]"
                                                           options="{'no_create': True, 'no_create_edit': True}"/>
                                                    <field name="quantity"/>
                                                    <field name="unit" readonly="1"/>
                                                    <field name="notes" placeholder="Optional notes"/>
                                                </tree>
                                            </field>
                                            <group>
                                                <field name="addl_requirements_other" string="Other Requirements"
                                                       placeholder="Specify any additional requirements not listed above"/>
                                            </group>
                                        </page>
                                    </notebook>

                                    <!-- Vendor, Revenue & Communication Details (match events presales exactly) -->
                                    <separator string="Vendor, Revenue &amp; Communication Details" colspan="2" invisible="enquiry_type != 'events_workshops'"/>
                                    <group col="2" invisible="enquiry_type != 'events_workshops'">
                                        <field name="event_sponsored"/>
                                        <field name="revenue_sharing"/>
                                        <field name="expected_revenue_type"/>
                                        <field name="expected_revenue_type_other"/>
                                        <field name="revenue_sharing_agreed"/>
                                        <field name="expected_revenue_range"/>
                                        <field name="proposed_revenue_split_tworks"/>
                                        <field name="proposed_revenue_split_vendor"/>
                                        <field name="ticketing_collection_by_tworks"/>
                                        <field name="preferred_communication"/>
                                    </group>
                                </page>
                            </notebook>
 
                            <!-- Navigation button at bottom of Requirements tab -->
                            <div class="d-flex justify-content-end mt-4">
                                <button name="action_move_to_presales" string="Next: Move to Pre-Sales" type="object" 
                                        class="btn btn-primary px-4" invisible="not can_move_to_presales"/>
                                <button name="action_show_presales_required" string="Next: Move to Pre-Sales" type="object" 
                                        class="btn btn-secondary px-4" invisible="can_move_to_presales"
                                        help="Complete the required fields before proceeding"/>
                            </div>
                        </page>

                        <!-- Pre-Sales tab -->
                        <page string="Pre-Sales" name="presales">
                            <div class ="d-flex justify-content-between align-items-center mb-3">
                            <group string="Scope of Work (SoW)">
                                <field name="sow_attachment_ids" widget="many2many_binary" nolabel="1"/>
                            </group>
                                <div>
                                    <h4>Standard Cost Estimates</h4>
                                </div>
                                <div>
                                    <button name="action_edit_cost_estimates" string="Edit Cost Estimates" type="object" 
                                            class="btn btn-secondary"/>
                                </div>
                            </div>
                            <group>
                                <group>
                                    <field name="project_deadline" readonly="1"/>
                                    <field name="design_time" readonly="1"/>
                                    <!-- Design cost -->
                                    <label for="design_cost"/>
                                    <div class="d-flex align-items-center">
                                        <field name="design_quantity" class="me-2" placeholder="Quantity" readonly="1"/>
                                        <span class="me-2">×</span>
                                        <field name="design_cost" string="Design cost (without GST per quantity)" placeholder="Cost per unit" readonly="1"/>
                                        <span class="ms-2">=</span>
                                        <field name="design_total" class="ms-2" readonly="1"/>
                                    </div>
                                    <label for="design_hsn" string="HSN/SAC"/>
                                    <field name="design_hsn" readonly="1" nolabel="1"/>
                                    <label for="material_cost"/>
                                    <field name="material_cost" readonly="1" nolabel="1"/>
                                    <!-- Prototyping cost -->
                                    <label for="prototyping_cost"/>
                                    <div class="d-flex align-items-center">
                                        <field name="prototyping_quantity" class="me-2" placeholder="Quantity" readonly="1"/>
                                        <span class="me-2">×</span>
                                        <field name="prototyping_cost" string="Prototyping cost (without GST per quantity)" placeholder="Cost per unit" readonly="1"/>
                                        <span class="ms-2">=</span>
                                        <field name="prototyping_total" class="ms-2" readonly="1"/>
                                    </div>
                                    <label for="prototyping_hsn" string="HSN/SAC"/>
                                    <field name="prototyping_hsn" readonly="1" nolabel="1"/>
                                    <field name="total_cost"/>
                                    <div class="d-flex justify-content-end mt-2">
                                        <button name="sync_presales_to_project"
                                                string="Sync to Project"
                                                type="object"
                                                class="btn btn-info btn-sm"
                                                invisible="stage_id.name != 'Pre-Sales'"
                                                help="Sync presales data from enquiry to linked project"/>
                                    </div>
                                </group>
                                <!-- Event/Workshop costs: only show for events/workshops -->
                                <group >
                                    <field name="venue_cost" readonly="1"/>
                                    <field name="additional_costs" readonly="1"/>
                                    <field name="gst_percentage" readonly="1"/>
                                </group>
                            </group>
                            
                            <group string="Pro Forma Invoice Management" invisible="not quotation_approved">
                                <div class="d-flex flex-column">
                                    <!-- No invoice created yet -->
                                    <div invisible="proforma_invoice_count > 0" class="d-flex align-items-center mb-3">
                                        <span class="text-muted me-3">No invoice created yet</span>
                                        <button name="action_generate_proforma_invoice" 
                                                string="Generate Invoice" 
                                                type="object" 
                                                class="btn btn-primary"/>
                                    </div>

                                    <div invisible="proforma_invoice_count == 0" class="mb-3">
                                        <!-- Status on one line -->
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fa fa-check-circle text-success me-2"></i>
                                            <span class="text-success" style="min-width: 180px;">Pro Forma Invoice exists</span>
                                        </div>
                                        
                                        <!-- Buttons below on a separate line -->
                                        <div class="d-flex mt-2">
                                            <button name="action_generate_proforma_invoice" 
                                                    string="View/Update Invoice" 
                                                    type="object" 
                                                    class="btn btn-secondary me-2"
                                                    help="View existing invoice. If costs have changed, you'll be asked whether to update or create new."/>
                                            <!-- <button name="action_force_new_invoice" 
                                                    string="Create New Version" 
                                                    type="object" 
                                                    class="btn btn-secondary"
                                                    help="Create a new invoice version based on current costs."/> -->
                                        </div>
                                    </div>
                                    
                                    <!-- View all versions button -->
                                    <div invisible="proforma_invoice_count &lt;= 1" class="mb-2">
                                        <button name="action_view_proforma_invoices" 
                                                string="View All Invoice Versions" 
                                                type="object"
                                                class="btn btn-link ps-0"/>
                                    </div>
                                </div>
                            </group>
                            
                            <!-- Payment Information Section -->
                            <group string="Payment Information" invisible="not quotation_approved">
                                <field name="payment_link" readonly="1"/>
                                <div class="d-flex mb-2">
                                    <button name="action_refresh_payment_link" string="Refresh Link" type="object" class="btn btn-secondary me-2" invisible="payment_status == 'paid'"/>
                                    <button name="action_update_invoice_attachments" string="Update Invoice Attachments" type="object" class="btn btn-secondary" invisible="proforma_invoice_count == 0"/>
                                </div>
                                <label for="quote_attachment_ids" string="Quote Documents"/>
                                <field name="quote_attachment_ids" widget="many2many_binary" readonly="1" nolabel="1"/>
                            </group>
                            
                            <div class="o_horizontal_separator mb8 mt16"><strong>Custom Line Items</strong></div>
                            <field name="custom_line_item_ids" nolabel="1" options="{'no_open': True}" readonly="1">
                                <tree>
                                    <field name="name" string="Service name"/>
                                    <field name="cost" string="Service cost"/>
                                    <field name="notes" string="Description"/>
                                </tree>
                            </field>
 
                            <div class="mt-4 d-flex justify-content-between">
                                <div>
                                    <button name="action_get_json" string="Get JSON" type="object" class="btn btn-secondary me-2"/>
                                    <button string="TEST BUTTON" type="object" class="btn btn-danger" name="action_get_json"/>
                                </div>

                                <!-- Navigation button at bottom of Pre-Sales tab with Approve Quotation button added -->
                                <div>
                                    <!-- Add Approve Quotation button here -->
                                    <button name="action_approve_quotation"
                                            string="Approve Quotation"
                                            type="object"
                                            class="btn btn-success me-2"
                                            groups="enquiry.group_enquiry_manager"
                                            invisible="quotation_approved"/>

                                    <button name="action_send_for_approval"
                                            string="Send for Approval"
                                            type="object"
                                            class="btn btn-primary px-4"
                                            invisible="quotation_approved"/>
                                </div>
                            </div>
                        </page>
                        <page string="Payment" name="payment">
                            <group>
                                <group string="Payment Status">
                                    <field name="payment_status" readonly="1"/>
                                </group>
                            </group>
 
                            <div class="o_horizontal_separator mb-3"><strong>Payment Options</strong></div>
 
                            <div class="mb-4">
                                <h5>Option 1: Online Payment</h5>
                                <div class="d-flex align-items-center">
                                    <button name="action_confirm_payment" string="Confirm Payment" type="object" 
                                            class="btn btn-primary me-2"/>
                                    <span class="text-muted">Payment must be completed to proceed to Active stage</span>
                                </div>
                            </div>
 
                            <div class="mb-4">
                                <h5>Option 2: Purchase Order</h5>
                                <group>
                                    <field name="po_attachment_ids" widget="many2many_binary" string="Purchase Order Document"/>
                                    <button name="action_submit_po" string="Submit PO for Approval" type="object" 
                                            class="btn btn-primary mb-2" style="min-width: 160px; white-space: nowrap;"/>
                                </group>
                            </div>
 
                            <div class="mb-4">
                                <h5>Option 3: Bank Transfer</h5>
                                <group>
                                    <field name="utr_id" string="UTR/Reference ID"/>
                                    <field name="utr_screenshot_ids" widget="many2many_binary" string="Payment Screenshot"/>
                                    <button name="action_submit_utr" string="Submit Bank Transfer Details" type="object" 
                                            class="btn btn-primary mb-2" style="min-width: 180px; white-space: nowrap;"/>
                                </group>
                            </div>
 
                            <!-- Navigation button at bottom of Payment tab -->
                            <div class="d-flex justify-content-end mt-4">
                                <button name="action_move_to_active" string="Next: Move to Active Stage" type="object" 
                                        class="btn btn-primary px-4"/>
                            </div>
                        </page>
 
                        <page string="Stage History" name="stage_history">
                            <group>
                                <group>
                                    <field name="stage_time_start" readonly="1"/>
                                    <field name="stage_duration_human" readonly="1"/>
                                </group>
                            </group>
                            <separator string="Stage History"/>
                            <field name="stage_history_ids" readonly="1">
                                <tree>
                                    <field name="stage_name"/>
                                    <field name="date_start"/>
                                    <field name="date_end"/>
                                    <field name="time_spent_human"/>
                                </tree>
                            </field>
                        </page>
                        
                        <page string="Closure" name="closure">
                            <group>
                                <field name="close_reason" widget="radio" options="{'horizontal': False}"/>
                                <field name="close_reason_notes" placeholder="Specify reason..." 
                                    invisible="close_reason != 'other'" 
                                    required="close_reason == 'other'"/>
                               <field name="closed_date" readonly="1"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Active Tab-->
    <record id="view_enquiry_form_active_tab" model="ir.ui.view">
        <field name="name">enquiry.enquiry.form.active.tab</field>
        <field name="model">enquiry.enquiry</field>
        <field name="inherit_id" ref="enquiry.view_enquiry_form"/>
        <field name="arch" type="xml">
            <!-- Remove "action_close_enquiry" button from header -->
            <xpath expr="//header/button[@name='action_close_enquiry']" position="replace"/>
            
            <!-- Add new Active tab before the Stage History tab -->
            <xpath expr="//page[@name='stage_history']" position="before">
                <page string="Active" name="active_management">
                    <group>
                        <group string="Project Management">
                            <field name="company_poc_id"/>
                            <field name="bd_poc_id"/>
                            <field name="followup_date"/>
                        </group>
                        <group string="Project Timeline">
                            <field name="project_deadline"/>
                            <field name="requested_deadline_date" invisible="enquiry_type != 'design_engineering'"/>
                        </group>
                    </group>
                    
                    <!-- Documents Section -->
                    <group string="Project Documents">
                        <!-- Existing Attachments -->
                        <field name="attachment_ids" widget="many2many_binary" string="General Attachments"/>
                        
                        <!-- Quote Documents -->
                        <field name="quote_attachment_ids" widget="many2many_binary" string="Quote Documents"/>
                        
                        <!-- Purchase Order Attachments -->
                        <field name="po_attachment_ids" widget="many2many_binary" string="Purchase Order Documents"/>
                        
                        <!-- SOW Attachments -->
                        <field name="sow_attachment_ids" widget="many2many_binary" string="Scope of Work"/>
                        
                        <!-- Work Report -->
                        <field name="deliverables_attachment_ids" widget="many2many_binary" string="Work Report"/>
                    </group>
                    
                    <!-- Capability-specific attachments -->
                    <group string="Fabrication Attachments" invisible="enquiry_type != 'fabrication_prototyping'">
                        <!-- Metal Shop Attachments -->
                        <field name="metal_shop_attachment_ids" widget="many2many_binary" string="Metal Shop Files"/>
                        
                        <!-- 3D Printing Attachments -->
                        <field name="printing_3d_attachment_ids" widget="many2many_binary" string="3D Printing Files"/>
                        
                        <!-- Laser Cutting Attachments -->
                        <field name="laser_cutting_attachment_ids" widget="many2many_binary" string="Laser Cutting Files"/>
                        
                        <!-- Wood Shop Attachments -->
                        <field name="wood_shop_attachment_ids" widget="many2many_binary" string="Wood Shop Files"/>
                        
                        <!-- Ceramic Studio Attachments -->
                        <field name="ceramic_studio_attachment_ids" widget="many2many_binary" string="Ceramic Studio Files"/>
                        
                        <!-- ARP Attachments -->
                        <field name="arp_attachment_ids" widget="many2many_binary" string="Advanced Rapid Prototyping Files"/>
                        
                        <!-- Electronics Attachments -->
                        <field name="electronics_attachment_ids" widget="many2many_binary" string="Electronics Files"/>
                        
                        <!-- Textile Attachments -->
                        <field name="textile_attachment_ids" widget="many2many_binary" string="Textile Files"/>
                    </group>
                    
                    <!-- Project Status section -->
                    <group string="Project Status">
                        <field name="stage_time_start" readonly="1"/>
                        <field name="stage_duration_human" readonly="1"/>
                    </group>
                    
                    <!-- Project Notes field -->
                    <group string="Project Notes">
                        <field name="project_notes" placeholder="Add project notes, updates, or important information..." style="min-height: 200px; width: 100%;" class="o_text_overflow"/>
                    </group>
                    
                    <!-- Bottom buttons -->
                    <div class="d-flex justify-content-between mt-4">
                        <div>
                            <button name="action_mark_completed" string="Mark as Completed" type="object" 
                                    class="btn btn-success"/>
                            <button name="action_move_to_dispatch" string="Move to Dispatch" type="object" 
                                    class="btn btn-primary"/>
                        </div>
                        <div>
                            <button name="action_close_enquiry" string="Close Enquiry" type="object" 
                                    class="btn btn-danger"/>
                        </div>
                    </div>
                </page>
            </xpath>
        </field>
    </record>

    <!-- Dispatch Tab-->
    <record id="view_enquiry_form_dispatch_tab" model="ir.ui.view">
        <field name="name">enquiry.enquiry.form.dispatch.tab</field>
        <field name="model">enquiry.enquiry</field>
        <field name="inherit_id" ref="enquiry.view_enquiry_form"/>
        <field name="arch" type="xml">
            <!-- Add Dispatch tab before the Stage History tab -->
            <xpath expr="//page[@name='stage_history']" position="before">
                <page string="Dispatch" name="dispatch_management">
                    <group>
                        <group string="Dispatch Documents">
                            <!-- Dispatch-specific attachment fields -->
                            <field name="gate_pass_attachment_ids" widget="many2many_binary" string="Gate Pass"/>
                            <field name="invoice_attachment_ids" widget="many2many_binary" string="Invoice"/>
                            <field name="delivery_challan_attachment_ids" widget="many2many_binary" string="Delivery Challan"/>
                            <field name="dispatch_photos_attachment_ids" widget="many2many_binary" string="Photos"/>
                            <field name="dispatch_other_attachment_ids" widget="many2many_binary" string="Other Documents"/>
                        </group>
                    </group>
                    
                    <!-- Move the "Mark as Completed" button from Active tab to here -->
                    <div class="d-flex justify-content-between mt-4">
                        <div>
                            <button name="action_generate_invoice" string="Generate Final Invoice" type="object" 
                                    class="btn btn-primary me-2" invisible="payment_status != 'paid'"/>
                        </div>
                        <div>
                            <button name="action_mark_completed" string="Mark as Completed" type="object" 
                                    class="btn btn-success px-4"/>
                        </div>
                    </div>
                </page>
            </xpath>
            
            <!-- Remove "Mark as Completed" button from Active tab -->
            <xpath expr="//page[@name='active_management']//button[@name='action_mark_completed']" position="replace"/>
        </field>
    </record>

    <!-- Action to view enquiries awaiting approval -->
    <record id="action_enquiries_awaiting_approval" model="ir.actions.act_window">
        <field name="name">Enquiries Awaiting Approval</field>
        <field name="res_model">enquiry.enquiry</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="domain">[('quotation_approved', '=', False), ('stage_id.name', '=', 'Pre-Sales')]</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No enquiries awaiting approval
            </p>
        </field>
    </record>

    <!-- Add menu item for enquiries awaiting approval -->
    <menuitem id="menu_enquiries_awaiting_approval" 
              name="Awaiting Approval" 
              parent="enquiry.menu_enquiry" 
              action="action_enquiries_awaiting_approval" 
              sequence="25"/>
</odoo>